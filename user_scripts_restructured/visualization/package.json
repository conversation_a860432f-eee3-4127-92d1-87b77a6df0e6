{"name": "nautilus-trader-visualization", "version": "3.0.0", "type": "module", "description": "Vue.js + ECharts visualization system for Nautilus Trader with reactive data management, infinite scrolling, and real-time updates", "main": "index.js", "scripts": {"build": "vite build", "build:check": "vue-tsc && vite build", "dev": "vite --host 0.0.0.0 --port 5173", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "clean": "rm -rf dist/ static/js/", "build:prod": "npm run clean && npm run type-check && npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@vueuse/core": "^10.9.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.0.0", "echarts": "^5.6.0", "pinia": "^2.1.7", "socket.io-client": "^4.7.5", "vue": "^3.4.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/dom": "^9.3.0", "@testing-library/jest-dom": "^6.1.0", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-jest": "^29.1.0", "typescript": "^5.8.3", "vite": "^5.0.0", "vue-tsc": "^2.2.10"}, "keywords": ["trading", "charts", "echarts", "vue", "vue3", "typescript", "lazy-loading", "streaming", "nautilus-trader", "visualization", "reactive", "pinia"], "author": "Nautilus Trader Team", "license": "MIT"}