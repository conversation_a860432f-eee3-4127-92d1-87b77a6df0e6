<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct ECharts Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        #chart-container {
            width: 100%;
            height: 600px;
            background: #1a1a1a;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4aa00; color: #000; }
        .error { background: #ff4444; }
    </style>
</head>
<body>
    <h1>🚀 Direct ECharts Test - No vue-echarts Dependency</h1>
    <div id="status" class="status">Initializing...</div>
    <div id="chart-container"></div>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5.6.0/dist/echarts.min.js"></script>
    <script>
        const statusEl = document.getElementById('status');
        const chartContainer = document.getElementById('chart-container');
        
        function updateStatus(message, isSuccess = false) {
            statusEl.textContent = message;
            statusEl.className = `status ${isSuccess ? 'success' : 'error'}`;
            console.log(message);
        }

        try {
            updateStatus('📊 Initializing direct ECharts...');
            
            // Test direct ECharts initialization (same pattern as VueEChartsChart.vue)
            const chartInstance = echarts.init(chartContainer, null, {
                renderer: 'canvas',
                devicePixelRatio: 1
            });
            
            updateStatus('✅ Direct ECharts instance created successfully!', true);
            
            // Test data in ECharts candlestick format [open, close, low, high]
            const testData = [
                [2320.26, 2302.6, 2287.3, 2362.94],
                [2300, 2291.3, 2288.26, 2308.38],
                [2295.35, 2346.5, 2295.35, 2346.92],
                [2347.22, 2358.98, 2337.35, 2363.8],
                [2360.75, 2382.48, 2347.89, 2383.76],
                [2383.43, 2385.42, 2371.23, 2391.82],
                [2377.41, 2419.02, 2369.57, 2421.15]
            ];
            
            const categoryData = ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05', '2023-01-06', '2023-01-07'];
            
            // ECharts option (same structure as VueEChartsChart.vue)
            const option = {
                backgroundColor: '#1a1a1a',
                title: {
                    text: 'Direct ECharts Candlestick Test',
                    left: 'center',
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: categoryData,
                    boundaryGap: false,
                    axisLine: {
                        lineStyle: {
                            color: '#cccccc'
                        }
                    },
                    axisLabel: {
                        color: '#cccccc'
                    }
                },
                yAxis: {
                    type: 'value',
                    scale: true,
                    axisLine: {
                        lineStyle: {
                            color: '#cccccc'
                        }
                    },
                    axisLabel: {
                        color: '#cccccc'
                    }
                },
                series: [
                    {
                        name: 'Test Data',
                        type: 'candlestick',
                        data: testData,
                        itemStyle: {
                            color: '#00d4aa',
                            color0: '#ff4444',
                            borderColor: '#00d4aa',
                            borderColor0: '#ff4444'
                        }
                    }
                ]
            };
            
            // Set option (same as VueEChartsChart.vue line 1236)
            chartInstance.setOption(option);
            updateStatus('✅ Chart rendered successfully! Vue-echarts dependency successfully removed.', true);
            
            // Test resize functionality
            window.addEventListener('resize', () => {
                chartInstance.resize();
            });
            
        } catch (error) {
            updateStatus(`❌ Error: ${error.message}`);
            console.error('Chart initialization failed:', error);
        }
    </script>
</body>
</html>