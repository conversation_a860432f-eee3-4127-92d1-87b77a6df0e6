"""
Highcharts visualization module for Nautilus Trader.

This module provides a parallel implementation to the ECharts system,
offering an alternative high-performance web server for viewing Nautilus Trader
parquet data as interactive charts using Highcharts library.

Features:
- Memory-efficient handling of large time series datasets
- Dynamic timeframe selection without data reloading
- Interactive OHLCV candlestick charts with volume subplot
- TradingView light theme styling
- Responsive design with full webpage occupation
- Client-side data caching and view persistence
"""

from .config import HighchartsConfig
from .app import HighchartsServer

__all__ = ['HighchartsConfig', 'HighchartsServer']
