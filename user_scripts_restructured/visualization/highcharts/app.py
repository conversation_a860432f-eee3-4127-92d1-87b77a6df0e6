"""
Highcharts Flask application for Nautilus Trader visualization.

This module provides a parallel implementation to the ECharts system,
reusing the existing data pipeline and APIs while providing Highcharts-based charts.
"""

import logging
import os
import sys
from pathlib import Path
from flask import Flask, render_template, request, jsonify
from typing import Dict, Any, Optional

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from config import HighchartsConfig

# Import existing data utilities from ECharts system
try:
    from user_scripts_restructured.visualization.vue_echarts.data_loader import (
        load_chart_data_with_direct_pyarrow_pipeline,
        get_available_instruments
    )
    from user_scripts_restructured.visualization.vue_echarts.validation import (
        validate_chart_data_request,
        validate_instrument_id,
        create_error_response
    )
    from user_scripts_restructured.visualization.vue_echarts.data_discovery import (
        get_instrument_date_range,
        get_instrument_summary,
        get_instrument_timeframes,
        check_instrument_exists
    )
except ImportError as e:
    logging.error(f"Failed to import ECharts modules: {e}")
    # Fallback implementations will be added if needed


class HighchartsServer:
    """Highcharts visualization server."""
    
    def __init__(self, config: HighchartsConfig):
        """Initialize the Highcharts server."""
        self.config = config
        self.config.validate()
        
        # Setup logging
        logging.basicConfig(
            level=getattr(logging, config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Create Flask app
        self.app = Flask(
            __name__,
            static_folder=config.get_static_folder(),
            template_folder=config.get_template_folder()
        )
        
        # Configure Flask
        self.app.config['DEBUG'] = config.debug
        self.app.config['JSON_SORT_KEYS'] = False
        
        # Register routes
        self._register_routes()
        
        self.logger.info(f"Highcharts server initialized on {config.host}:{config.port}")

    def _register_routes(self):
        """Register Flask routes."""

        @self.app.route('/')
        def index():
            """Render the main Highcharts page."""
            try:
                instruments = get_available_instruments()
                return render_template(
                    'index.html',
                    instruments=instruments,
                    default_instrument=self.config.default_instrument,
                    default_timeframe=self.config.default_timeframe,
                    available_timeframes=self.config.available_timeframes
                )
            except Exception as e:
                self.logger.error(f"Error loading index page: {e}")
                return render_template(
                    'index.html',
                    instruments=[],
                    default_instrument=self.config.default_instrument,
                    default_timeframe=self.config.default_timeframe,
                    available_timeframes=self.config.available_timeframes
                )

        @self.app.route('/highcharts/')
        def highcharts_index():
            """Alternative route with prefix."""
            return index()

        @self.app.route('/highcharts/chart/<instrument_id>')
        def chart(instrument_id: str):
            """Render chart for specific instrument."""
            timeframe = request.args.get('timeframe', self.config.default_timeframe)
            debug = request.args.get('debug', 'false').lower() == 'true'
            
            return render_template(
                'index.html',
                instruments=[instrument_id],
                default_instrument=instrument_id,
                default_timeframe=timeframe,
                available_timeframes=self.config.available_timeframes,
                debug=debug
            )

        @self.app.route('/api/instruments')
        def get_instruments():
            """Return available instruments as JSON."""
            try:
                instruments = get_available_instruments()
                return jsonify(instruments)
            except Exception as e:
                self.logger.error(f"Error getting instruments: {e}")
                return jsonify(create_error_response(
                    "Failed to retrieve instruments list",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/chart-data/<instrument_id>')
        def get_chart_data(instrument_id: str):
            """Return chart data for a specific instrument (reuses ECharts API)."""
            try:
                # Prepare request args for validation
                request_args = {
                    'instrument_id': instrument_id,
                    'timeframe': request.args.get('timeframe', '1min'),
                    'limit': request.args.get('limit', str(self.config.max_points)),
                    'before_timestamp_seconds': request.args.get('before_timestamp_seconds'),
                    'after_timestamp_seconds': request.args.get('after_timestamp_seconds')
                }
                
                # Validate request
                is_valid, error_response, validation_result = validate_chart_data_request(request_args)
                if not is_valid:
                    return jsonify(error_response), 400

                # Extract validated parameters
                timeframe = validation_result['timeframe']
                limit = validation_result['limit']
                before_datetime = validation_result.get('before_datetime')
                after_datetime = validation_result.get('after_datetime')
                
                # Use direct PyArrow pipeline for performance
                response_data = load_chart_data_with_direct_pyarrow_pipeline(
                    instrument_dir=None,  # Will be resolved from catalog
                    instrument_id=instrument_id,
                    start_date=None,
                    end_date=None,
                    columns=None,
                    before_datetime=before_datetime.isoformat() if before_datetime else None,
                    after_datetime=after_datetime.isoformat() if after_datetime else None,
                    limit=limit,
                    format_type='highcharts',  # Request Highcharts-compatible format
                    pipeline_mode='direct_pyarrow'
                )
                
                return jsonify(response_data)
                
            except Exception as e:
                self.logger.error(f"Error getting chart data for {instrument_id}: {e}")
                return jsonify(create_error_response(
                    f"Failed to retrieve chart data for {instrument_id}",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

        @self.app.route('/api/instruments/<instrument_id>/summary')
        def get_instrument_summary_route(instrument_id: str):
            """Get comprehensive summary for an instrument."""
            try:
                # Validate instrument ID
                is_valid, error_msg = validate_instrument_id(instrument_id)
                if not is_valid:
                    return jsonify(create_error_response(error_msg, "instrument_id", "INVALID_INSTRUMENT_ID")), 400
                
                # Get summary
                summary = get_instrument_summary(instrument_id)
                if summary is None:
                    return jsonify(create_error_response(
                        f"No data found for instrument {instrument_id}",
                        "instrument_id", 
                        "INSTRUMENT_NOT_FOUND"
                    )), 404
                
                return jsonify(summary)
                
            except Exception as e:
                self.logger.error(f"Error getting instrument summary for {instrument_id}: {e}")
                return jsonify(create_error_response(
                    f"Failed to retrieve summary for {instrument_id}",
                    "server",
                    "INTERNAL_SERVER_ERROR"
                )), 500

    def run(self):
        """Run the Flask development server."""
        self.logger.info(f"Starting Highcharts server on http://{self.config.host}:{self.config.port}")
        self.app.run(
            host=self.config.host,
            port=self.config.port,
            debug=self.config.debug,
            threaded=True
        )

    def get_app(self):
        """Get the Flask app instance for WSGI deployment."""
        return self.app


def create_app(config: Optional[HighchartsConfig] = None) -> Flask:
    """Factory function to create Flask app."""
    if config is None:
        config = HighchartsConfig.from_env()
    
    server = HighchartsServer(config)
    return server.get_app()


if __name__ == '__main__':
    # Create and run server
    config = HighchartsConfig.from_env(debug=True)
    server = HighchartsServer(config)
    server.run()
