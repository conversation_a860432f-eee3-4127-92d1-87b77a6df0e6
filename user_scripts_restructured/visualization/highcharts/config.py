"""
Configuration module for Highcharts visualization server.
"""

from dataclasses import dataclass, field
from typing import List, Optional
import os


@dataclass
class HighchartsConfig:
    """Configuration for Highcharts visualizer."""

    # Server configuration
    host: str = "0.0.0.0"
    port: int = 8083  # Different port from ECharts (8082)
    debug: bool = False
    log_level: str = "INFO"

    # Data configuration
    catalog_path: str = "/home/<USER>/nautilus_trader_fork/catalog"
    max_points: int = 50000  # Per-chunk limit for performance
    cache_expiry: int = 1800  # 30 minutes
    max_cache_items: int = 500

    # Chart-specific configuration
    enable_websocket: bool = False
    websocket_port: int = 8084
    enable_test_data: bool = False
    test_symbols: str = "MNQFUT.CME,MNQ.CME,ES.CME"
    test_bars: int = 1000

    # Performance configuration
    enable_compression: bool = True
    enable_caching: bool = True
    enable_lazy_loading: bool = True
    chunk_size: int = 1000  # Data points per chunk
    
    # UI configuration
    default_timeframe: str = "1min"
    default_instrument: str = "MNQ.CME"  # User preference
    available_timeframes: list = field(default_factory=lambda: [
        "1min", "5min", "15min", "30min", "1h", "4h", "1d"
    ])

    # Theme configuration
    theme: str = "tradingview_light"  # Only light theme as per user preference
    enable_dark_theme: bool = False  # Explicitly disabled

    @classmethod
    def from_env(cls, **overrides) -> 'HighchartsConfig':
        """
        Create HighchartsConfig from environment variables.
        
        Args:
            **overrides: Additional configuration overrides
        
        Returns:
            HighchartsConfig instance
        """
        config_dict = {
            'host': os.getenv('HIGHCHARTS_HOST', '0.0.0.0'),
            'port': int(os.getenv('HIGHCHARTS_PORT', '8083')),
            'debug': os.getenv('HIGHCHARTS_DEBUG', 'false').lower() == 'true',
            'catalog_path': os.getenv('CATALOG_PATH', '/home/<USER>/nautilus_trader_fork/catalog'),
            'max_points': int(os.getenv('HIGHCHARTS_MAX_POINTS', '50000')),
            'enable_websocket': os.getenv('HIGHCHARTS_WEBSOCKET', 'false').lower() == 'true',
            'default_instrument': os.getenv('HIGHCHARTS_DEFAULT_INSTRUMENT', 'MNQ.CME'),
        }
        
        # Apply overrides
        config_dict.update(overrides)
        
        return cls(**config_dict)

    def validate(self) -> bool:
        """
        Validate configuration settings.
        
        Returns:
            bool: True if configuration is valid
        """
        if not os.path.exists(self.catalog_path):
            raise ValueError(f"Catalog path does not exist: {self.catalog_path}")
        
        if self.port < 1024 or self.port > 65535:
            raise ValueError(f"Invalid port number: {self.port}")
        
        if self.max_points <= 0:
            raise ValueError(f"max_points must be positive: {self.max_points}")
        
        if self.default_timeframe not in self.available_timeframes:
            raise ValueError(f"default_timeframe must be in available_timeframes")
        
        return True

    def get_static_folder(self) -> str:
        """Get the static folder path."""
        return os.path.join(os.path.dirname(__file__), 'static')

    def get_template_folder(self) -> str:
        """Get the template folder path."""
        return os.path.join(os.path.dirname(__file__), 'templates')
