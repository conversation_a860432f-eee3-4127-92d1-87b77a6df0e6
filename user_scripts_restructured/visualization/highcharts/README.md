# Highcharts Visualization for Nautilus Trader

A parallel implementation of the Nautilus Trader visualization system using Highcharts instead of ECharts. This provides an alternative high-performance charting solution while maintaining full compatibility with the existing data pipeline.

## Features

### Core Functionality
- **Interactive OHLCV Charts**: Candlestick charts with volume subplot
- **Multiple Timeframes**: 1min, 5min, 15min, 30min, 1h, 4h, 1d
- **Real-time Performance**: No data reloading when switching timeframes
- **TradingView Light Theme**: Professional trading interface styling
- **Responsive Design**: Full webpage occupation with no blank spaces

### Technical Features
- **Parallel Architecture**: Runs independently alongside ECharts system
- **Data Pipeline Integration**: Reuses existing Nautilus Trader data APIs
- **Client-side Caching**: Efficient data management and view persistence
- **Performance Optimized**: WebGL rendering and lazy loading support
- **Keyboard Shortcuts**: Quick timeframe switching and chart controls

## Quick Start

### 1. Installation

No additional dependencies required - uses existing Nautilus Trader environment.

### 2. Start the Server

```bash
# Navigate to the highcharts directory
cd user_scripts_restructured/visualization/highcharts

# Run the Flask application
python app.py
```

### 3. Access the Interface

- **Main Interface**: http://localhost:8083
- **Specific Instrument**: http://localhost:8083/highcharts/chart/MNQFUT.CME
- **With Timeframe**: http://localhost:8083/highcharts/chart/MNQFUT.CME?timeframe=5min

## Configuration

### Environment Variables

```bash
export HIGHCHARTS_HOST=0.0.0.0
export HIGHCHARTS_PORT=8083
export HIGHCHARTS_DEBUG=true
export CATALOG_PATH=/path/to/nautilus/catalog
export HIGHCHARTS_DEFAULT_INSTRUMENT=MNQFUT.CME
```

### Configuration File

Edit `config.py` to customize:

```python
config = HighchartsConfig(
    host="0.0.0.0",
    port=8083,
    default_instrument="MNQFUT.CME",
    default_timeframe="1min",
    max_points=50000,
    enable_caching=True
)
```

## API Endpoints

### Chart Data
- `GET /api/chart-data/<instrument_id>` - Get OHLCV data
- `GET /api/instruments` - List available instruments
- `GET /api/instruments/<instrument_id>/summary` - Instrument details

### Parameters
- `timeframe`: 1min, 5min, 15min, 30min, 1h, 4h, 1d
- `limit`: Maximum number of data points (default: 50000)
- `before_timestamp_seconds`: For pagination
- `after_timestamp_seconds`: For pagination

## Architecture

### Directory Structure
```
highcharts/
├── app.py                 # Flask application entry point
├── config.py              # Configuration management
├── static/
│   ├── js/
│   │   ├── chart-config.js    # Highcharts configuration
│   │   ├── data-fetcher.js    # API communication
│   │   ├── ui-controls.js     # User interface controls
│   │   └── utils.js           # Utility functions
│   └── css/
│       └── styles.css         # TradingView light theme
├── templates/
│   └── index.html             # Main chart page
└── README.md                  # This file
```

### Component Overview

1. **Flask Backend** (`app.py`)
   - Reuses existing ECharts data pipeline
   - Provides REST API endpoints
   - Serves static files and templates

2. **Chart Configuration** (`chart-config.js`)
   - Highcharts setup and theming
   - TradingView light theme implementation
   - Candlestick and volume chart configuration

3. **Data Management** (`data-fetcher.js`)
   - API communication with caching
   - Data format conversion
   - Performance optimization

4. **User Interface** (`ui-controls.js`)
   - Timeframe selection
   - Instrument switching
   - Chart interactions and shortcuts

## Usage

### Basic Operations

1. **Change Timeframe**: Click timeframe buttons or use keyboard shortcuts (1-7)
2. **Switch Instrument**: Use dropdown selector
3. **Pan/Zoom**: Mouse drag and scroll wheel
4. **Refresh Data**: Click refresh button or Ctrl+R
5. **Fullscreen**: Click fullscreen button or Ctrl+F

### Keyboard Shortcuts

- `1-7`: Switch timeframes (1=1min, 2=5min, etc.)
- `Ctrl+R`: Refresh data
- `Ctrl+F`: Toggle fullscreen
- `Shift+Drag`: Pan chart horizontally

### Data Integration

The system automatically integrates with existing Nautilus Trader data:

1. **Parquet Files**: Reads from catalog directory
2. **Data Validation**: Uses existing validation pipeline
3. **Format Conversion**: Converts to Highcharts-compatible format
4. **Caching**: Implements intelligent caching for performance

## Comparison with ECharts System

| Feature | Highcharts | ECharts |
|---------|------------|---------|
| **Port** | 8083 | 8082 |
| **Theme** | TradingView Light | TradingView Light |
| **Performance** | WebGL + Caching | Vue.js + WebSocket |
| **Data Pipeline** | Shared | Shared |
| **Timeframes** | Client-side switching | Client-side switching |
| **Volume Display** | Subplot | Subplot |
| **Responsive** | Full occupation | Full occupation |
| **Dependencies** | Vanilla JS | Vue.js + TypeScript |

## Troubleshooting

### Common Issues

1. **Port Conflict**: Change port in config.py if 8083 is in use
2. **Data Not Loading**: Check catalog path and permissions
3. **Chart Not Rendering**: Verify Highcharts CDN access
4. **Performance Issues**: Reduce max_points in configuration

### Debug Mode

Enable debug mode for detailed logging:

```bash
export HIGHCHARTS_DEBUG=true
python app.py
```

Access debug tools in browser console:
```javascript
window.debugApp.chartManager
window.debugApp.dataFetcher
window.debugApp.utils
```

### Log Files

Check application logs for errors:
```bash
tail -f logs/highcharts.log
```

## Development

### Adding New Features

1. **New Chart Types**: Extend `chart-config.js`
2. **Additional APIs**: Add routes to `app.py`
3. **UI Components**: Modify `ui-controls.js`
4. **Styling**: Update `styles.css`

### Testing

```bash
# Test with sample data
python -c "from app import create_app; app = create_app(); app.run(debug=True)"

# Access test endpoints
curl http://localhost:8083/api/instruments
curl http://localhost:8083/api/chart-data/MNQFUT.CME?timeframe=1min&limit=100
```

## Performance Optimization

### Client-side Caching
- 30-minute cache expiry
- Automatic cache management
- Memory-efficient storage

### Data Loading
- Lazy loading for large datasets
- Pagination support
- Compression enabled

### Rendering
- WebGL acceleration
- Animation disabled for performance
- Responsive chart resizing

## Support

For issues specific to the Highcharts implementation:
1. Check this README for common solutions
2. Enable debug mode for detailed logging
3. Compare behavior with ECharts system
4. Verify data pipeline integrity

The Highcharts system is designed to be a drop-in alternative to ECharts while maintaining the same data quality and performance standards.
