/**
 * TradingView Light Theme Styles for Highcharts Nautilus Trader Visualization
 * Responsive design with full webpage occupation
 */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #131722;
    background-color: #FFFFFF;
    overflow: hidden; /* Prevent scrollbars for full occupation */
}

/* Application Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    width: 100vw;
}

/* Header */
.app-header {
    background-color: #F7F8FA;
    border-bottom: 1px solid #E0E3EB;
    padding: 8px 16px;
    flex-shrink: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
}

.app-title {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.title-main {
    font-size: 18px;
    font-weight: 600;
    color: #131722;
}

.title-sub {
    font-size: 12px;
    font-weight: 400;
    color: #787B86;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #787B86;
}

.separator {
    color: #E0E3EB;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Chart Container */
.chart-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: #FFFFFF;
}

/* Toolbar */
.chart-toolbar {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 8px 16px;
    background-color: #F7F8FA;
    border-bottom: 1px solid #E0E3EB;
    flex-wrap: wrap;
    flex-shrink: 0;
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-section label {
    font-size: 12px;
    font-weight: 500;
    color: #787B86;
    white-space: nowrap;
}

.toolbar-info {
    margin-left: auto;
}

/* Controls */
.control-select {
    padding: 4px 8px;
    border: 1px solid #E0E3EB;
    border-radius: 4px;
    background-color: #FFFFFF;
    color: #131722;
    font-size: 12px;
    min-width: 120px;
}

.control-select:focus {
    outline: none;
    border-color: #2962FF;
    box-shadow: 0 0 0 2px rgba(41, 98, 255, 0.1);
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border: 1px solid #E0E3EB;
    border-radius: 4px;
    background-color: #FFFFFF;
    color: #131722;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background-color: #F7F8FA;
    border-color: #2962FF;
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-icon {
    font-size: 14px;
}

/* Timeframe Buttons */
.timeframe-buttons {
    display: flex;
    gap: 2px;
    background-color: #E0E3EB;
    border-radius: 4px;
    padding: 2px;
}

.timeframe-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 2px;
    background-color: transparent;
    color: #787B86;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 40px;
}

.timeframe-btn:hover {
    background-color: #F7F8FA;
    color: #131722;
}

.timeframe-btn.active {
    background-color: #2962FF;
    color: #FFFFFF;
}

/* Data Info */
.data-info {
    font-size: 11px;
    color: #787B86;
    font-weight: 500;
}

/* Chart Area */
.chart-area {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.highcharts-chart {
    width: 100%;
    height: 100%;
}

/* Loading Indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #E0E3EB;
    z-index: 1000;
}

.loading-indicator.hidden {
    display: none;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #E0E3EB;
    border-top: 2px solid #2962FF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 12px;
    color: #787B86;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Bar */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 16px;
    background-color: #F7F8FA;
    border-top: 1px solid #E0E3EB;
    font-size: 11px;
    color: #787B86;
    flex-shrink: 0;
}

.status-section {
    display: flex;
    align-items: center;
    gap: 4px;
}

.status-indicator {
    font-size: 8px;
}

.status-indicator.connected {
    color: #26A69A;
}

.status-indicator.disconnected {
    color: #EF5350;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background-color: #FFFFFF;
    border-radius: 8px;
    border: 1px solid #E0E3EB;
    max-width: 400px;
    width: 90%;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #E0E3EB;
}

.modal-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #131722;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #787B86;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #131722;
}

.modal-body {
    padding: 16px;
    color: #131722;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid #E0E3EB;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.btn {
    padding: 8px 16px;
    border: 1px solid #E0E3EB;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #2962FF;
    color: #FFFFFF;
    border-color: #2962FF;
}

.btn-primary:hover {
    background-color: #1E4FCC;
    border-color: #1E4FCC;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chart-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }
    
    .toolbar-section {
        justify-content: space-between;
    }
    
    .toolbar-info {
        margin-left: 0;
    }
    
    .timeframe-buttons {
        flex-wrap: wrap;
    }
    
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .app-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: 6px 12px;
    }
    
    .chart-toolbar {
        padding: 6px 12px;
    }
    
    .status-bar {
        padding: 4px 12px;
        flex-direction: column;
        gap: 4px;
    }
    
    .timeframe-btn {
        min-width: 35px;
        padding: 3px 6px;
    }
}

/* Highcharts Overrides */
.highcharts-container {
    font-family: inherit !important;
}

.highcharts-background {
    fill: #FFFFFF !important;
}

.highcharts-plot-background {
    fill: #FFFFFF !important;
}

.highcharts-grid-line {
    stroke: #F0F3FA !important;
}

.highcharts-axis-line {
    stroke: #E0E3EB !important;
}

.highcharts-axis-labels text {
    fill: #787B86 !important;
    font-size: 11px !important;
}

.highcharts-title {
    fill: #131722 !important;
    font-weight: 600 !important;
}

.highcharts-crosshair {
    stroke: #2962FF !important;
    stroke-dasharray: 3,3 !important;
}

/* Print Styles */
@media print {
    .app-header,
    .chart-toolbar,
    .status-bar {
        display: none;
    }
    
    .chart-container {
        height: 100vh;
    }
}
