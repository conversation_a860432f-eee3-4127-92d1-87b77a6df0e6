/**
 * Highcharts Configuration for Nautilus Trader
 * TradingView Light Theme Implementation
 */

class HighchartsConfig {
    constructor() {
        this.theme = this.getTradingViewLightTheme();
        this.defaultOptions = this.getDefaultChartOptions();
    }

    /**
     * TradingView Light Theme Configuration
     */
    getTradingViewLightTheme() {
        return {
            colors: [
                '#2962FF', '#FF6D00', '#46BF75', '#F7931A', '#DC143C',
                '#FF1744', '#9C27B0', '#3F51B5', '#009688', '#FF9800'
            ],
            chart: {
                backgroundColor: '#FFFFFF',
                borderColor: '#E0E3EB',
                borderWidth: 1,
                className: 'highcharts-light',
                plotBackgroundColor: '#FFFFFF',
                plotBorderColor: '#E0E3EB',
                plotBorderWidth: 1
            },
            title: {
                style: {
                    color: '#131722',
                    fontSize: '16px',
                    fontWeight: '600'
                }
            },
            subtitle: {
                style: {
                    color: '#787B86',
                    fontSize: '12px'
                }
            },
            xAxis: {
                gridLineColor: '#F0F3FA',
                gridLineWidth: 1,
                lineColor: '#E0E3EB',
                minorGridLineColor: '#F7F8FA',
                tickColor: '#E0E3EB',
                title: {
                    style: {
                        color: '#787B86'
                    }
                },
                labels: {
                    style: {
                        color: '#787B86',
                        fontSize: '11px'
                    }
                }
            },
            yAxis: {
                gridLineColor: '#F0F3FA',
                gridLineWidth: 1,
                lineColor: '#E0E3EB',
                minorGridLineColor: '#F7F8FA',
                tickColor: '#E0E3EB',
                title: {
                    style: {
                        color: '#787B86'
                    }
                },
                labels: {
                    style: {
                        color: '#787B86',
                        fontSize: '11px'
                    }
                }
            },
            legend: {
                backgroundColor: 'rgba(255, 255, 255, 0.85)',
                borderColor: '#E0E3EB',
                borderWidth: 1,
                itemStyle: {
                    color: '#131722',
                    fontSize: '12px'
                },
                itemHoverStyle: {
                    color: '#2962FF'
                },
                itemHiddenStyle: {
                    color: '#CCCCCC'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderColor: '#E0E3EB',
                borderWidth: 1,
                style: {
                    color: '#131722',
                    fontSize: '12px'
                }
            },
            plotOptions: {
                series: {
                    dataLabels: {
                        color: '#131722'
                    },
                    marker: {
                        lineColor: '#FFFFFF'
                    }
                },
                candlestick: {
                    lineColor: '#131722',
                    upColor: '#26A69A',
                    upLineColor: '#26A69A',
                    color: '#EF5350',
                    lineWidth: 1
                },
                column: {
                    borderColor: '#E0E3EB'
                }
            },
            navigation: {
                buttonOptions: {
                    symbolStroke: '#787B86',
                    theme: {
                        fill: '#FFFFFF',
                        stroke: '#E0E3EB',
                        'stroke-width': 1,
                        states: {
                            hover: {
                                fill: '#F7F8FA',
                                stroke: '#2962FF'
                            },
                            select: {
                                fill: '#E3F2FD',
                                stroke: '#2962FF'
                            }
                        }
                    }
                }
            }
        };
    }

    /**
     * Default Chart Options
     */
    getDefaultChartOptions() {
        return {
            chart: {
                type: 'candlestick',
                height: '100%',
                animation: false, // Disable for better performance
                panning: {
                    enabled: true,
                    type: 'x'
                },
                panKey: 'shift',
                zooming: {
                    type: 'x'
                },
                events: {
                    load: function() {
                        console.log('Highcharts chart loaded');
                    }
                }
            },
            title: {
                text: null // Will be set dynamically
            },
            rangeSelector: {
                enabled: false // We'll use custom timeframe controls
            },
            navigator: {
                enabled: false // Simplified interface
            },
            scrollbar: {
                enabled: false
            },
            credits: {
                enabled: false
            },
            exporting: {
                enabled: true,
                buttons: {
                    contextButton: {
                        menuItems: [
                            'viewFullscreen',
                            'separator',
                            'downloadPNG',
                            'downloadJPEG',
                            'downloadPDF',
                            'downloadSVG'
                        ]
                    }
                }
            },
            xAxis: {
                type: 'datetime',
                crosshair: {
                    snap: false,
                    color: '#2962FF',
                    width: 1,
                    dashStyle: 'Dash'
                },
                dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%H:%M',
                    day: '%e %b',
                    week: '%e %b',
                    month: '%b %y',
                    year: '%Y'
                }
            },
            yAxis: [{
                // Main price axis
                labels: {
                    align: 'right',
                    x: -3
                },
                title: {
                    text: 'Price'
                },
                height: '70%',
                lineWidth: 2,
                resize: {
                    enabled: true
                },
                crosshair: {
                    snap: false,
                    color: '#2962FF',
                    width: 1,
                    dashStyle: 'Dash'
                }
            }, {
                // Volume axis
                labels: {
                    align: 'right',
                    x: -3
                },
                title: {
                    text: 'Volume'
                },
                top: '75%',
                height: '25%',
                offset: 0,
                lineWidth: 2
            }],
            tooltip: {
                split: false,
                shared: true,
                useHTML: true,
                formatter: function() {
                    let tooltip = `<div style="font-size: 12px;">`;
                    tooltip += `<div style="margin-bottom: 5px;"><strong>${Highcharts.dateFormat('%A, %b %e, %Y %H:%M', this.x)}</strong></div>`;
                    
                    this.points.forEach(point => {
                        if (point.series.name === 'Price') {
                            tooltip += `<div style="color: ${point.color};">`;
                            tooltip += `<strong>O:</strong> ${point.point.open.toFixed(2)} `;
                            tooltip += `<strong>H:</strong> ${point.point.high.toFixed(2)} `;
                            tooltip += `<strong>L:</strong> ${point.point.low.toFixed(2)} `;
                            tooltip += `<strong>C:</strong> ${point.point.close.toFixed(2)}`;
                            tooltip += `</div>`;
                        } else if (point.series.name === 'Volume') {
                            tooltip += `<div style="color: ${point.color};">`;
                            tooltip += `<strong>Volume:</strong> ${point.y.toLocaleString()}`;
                            tooltip += `</div>`;
                        }
                    });
                    
                    tooltip += `</div>`;
                    return tooltip;
                }
            },
            plotOptions: {
                candlestick: {
                    groupPadding: 0.01,
                    pointPadding: 0.01,
                    borderWidth: 1,
                    dataGrouping: {
                        enabled: false // We handle timeframe aggregation client-side
                    }
                },
                column: {
                    groupPadding: 0.01,
                    pointPadding: 0.01,
                    borderWidth: 0,
                    dataGrouping: {
                        enabled: false
                    }
                }
            },
            series: [], // Will be populated dynamically
            responsive: {
                rules: [{
                    condition: {
                        maxWidth: 768
                    },
                    chartOptions: {
                        yAxis: [{
                            height: '65%'
                        }, {
                            top: '70%',
                            height: '30%'
                        }]
                    }
                }]
            }
        };
    }

    /**
     * Create chart configuration for specific instrument and timeframe
     */
    createChartConfig(instrument, timeframe, data) {
        const config = Highcharts.merge(this.theme, this.defaultOptions);
        
        // Set title
        config.title = {
            text: `${instrument} - ${timeframe}`,
            style: this.theme.title.style
        };
        
        // Configure series
        config.series = [
            {
                name: 'Price',
                type: 'candlestick',
                data: data.ohlc || [],
                yAxis: 0,
                id: 'price-series'
            },
            {
                name: 'Volume',
                type: 'column',
                data: data.volume || [],
                yAxis: 1,
                color: 'rgba(41, 98, 255, 0.3)',
                id: 'volume-series'
            }
        ];
        
        return config;
    }

    /**
     * Apply theme to existing chart
     */
    applyTheme(chart) {
        chart.update(this.theme, true);
    }
}

// Export for use in other modules
window.HighchartsConfig = HighchartsConfig;
