/**
 * Data Fetcher for Highcharts Nautilus Trader Visualization
 * Handles API communication and data format conversion
 */

class DataFetcher {
    constructor() {
        this.cache = new Map();
        this.cacheExpiry = 30 * 60 * 1000; // 30 minutes
        this.baseUrl = window.location.origin;
        this.isLoading = false;
        this.abortController = null;
    }

    /**
     * Fetch chart data for instrument and timeframe
     */
    async fetchChartData(instrument, timeframe, options = {}) {
        const cacheKey = `${instrument}-${timeframe}-${JSON.stringify(options)}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheExpiry) {
                console.log(`Cache hit for ${instrument} ${timeframe}`);
                return cached.data;
            }
        }

        // Cancel previous request if still pending
        if (this.abortController) {
            this.abortController.abort();
        }
        this.abortController = new AbortController();

        try {
            this.isLoading = true;
            const startTime = performance.now();

            // Build API URL
            const params = new URLSearchParams({
                timeframe: timeframe,
                limit: options.limit || 50000
            });

            if (options.before_timestamp_seconds) {
                params.append('before_timestamp_seconds', options.before_timestamp_seconds);
            }
            if (options.after_timestamp_seconds) {
                params.append('after_timestamp_seconds', options.after_timestamp_seconds);
            }

            const url = `${this.baseUrl}/api/chart-data/${instrument}?${params}`;
            
            console.log(`Fetching data: ${url}`);

            const response = await fetch(url, {
                signal: this.abortController.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const rawData = await response.json();
            const loadTime = performance.now() - startTime;

            console.log(`Data loaded in ${Math.round(loadTime)}ms:`, {
                instrument,
                timeframe,
                bars: rawData.ohlc?.length || 0,
                dataSource: rawData.data_source
            });

            // Convert to Highcharts format
            const chartData = this.convertToHighchartsFormat(rawData);

            // Cache the result
            this.cache.set(cacheKey, {
                data: chartData,
                timestamp: Date.now()
            });

            return chartData;

        } catch (error) {
            if (error.name === 'AbortError') {
                console.log('Request aborted');
                return null;
            }
            console.error('Error fetching chart data:', error);
            throw error;
        } finally {
            this.isLoading = false;
            this.abortController = null;
        }
    }

    /**
     * Convert API response to Highcharts format
     */
    convertToHighchartsFormat(apiData) {
        if (!apiData || !apiData.ohlc || !Array.isArray(apiData.ohlc)) {
            console.warn('Invalid API data format:', apiData);
            return { ohlc: [], volume: [] };
        }

        const ohlcData = [];
        const volumeData = [];

        // Handle different data formats from the API
        if (apiData.ohlc.length > 0) {
            const firstBar = apiData.ohlc[0];
            
            if (Array.isArray(firstBar)) {
                // ECharts format: [[time, open, high, low, close], ...]
                apiData.ohlc.forEach((bar, index) => {
                    if (bar.length >= 5) {
                        const timestamp = this.parseTimestamp(bar[0]);
                        ohlcData.push([
                            timestamp,
                            parseFloat(bar[1]), // open
                            parseFloat(bar[2]), // high
                            parseFloat(bar[3]), // low
                            parseFloat(bar[4])  // close
                        ]);

                        // Add corresponding volume data
                        if (apiData.volume && apiData.volume[index]) {
                            const volBar = apiData.volume[index];
                            const volume = Array.isArray(volBar) ? volBar[1] : volBar.value || volBar;
                            volumeData.push([timestamp, parseFloat(volume)]);
                        }
                    }
                });
            } else if (typeof firstBar === 'object' && firstBar.time !== undefined) {
                // Object format: {time, open, high, low, close}
                apiData.ohlc.forEach((bar, index) => {
                    const timestamp = this.parseTimestamp(bar.time);
                    ohlcData.push([
                        timestamp,
                        parseFloat(bar.open),
                        parseFloat(bar.high),
                        parseFloat(bar.low),
                        parseFloat(bar.close)
                    ]);

                    // Add corresponding volume data
                    if (apiData.volume && apiData.volume[index]) {
                        const volBar = apiData.volume[index];
                        const volume = volBar.value || volBar.volume || volBar;
                        volumeData.push([timestamp, parseFloat(volume)]);
                    }
                });
            }
        }

        return {
            ohlc: ohlcData,
            volume: volumeData,
            instrument: apiData.instrument,
            timeframe: apiData.timeframe,
            bars_returned: apiData.bars_returned || ohlcData.length,
            statistics: apiData.statistics,
            data_quality: apiData.data_quality
        };
    }

    /**
     * Parse timestamp to milliseconds for Highcharts
     */
    parseTimestamp(timestamp) {
        if (typeof timestamp === 'number') {
            // Assume seconds if less than 13 digits, milliseconds otherwise
            return timestamp < 10000000000000 ? timestamp * 1000 : timestamp;
        }
        
        if (typeof timestamp === 'string') {
            const date = new Date(timestamp);
            return date.getTime();
        }
        
        return Date.now(); // Fallback
    }

    /**
     * Fetch available instruments
     */
    async fetchInstruments() {
        try {
            const response = await fetch(`${this.baseUrl}/api/instruments`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching instruments:', error);
            return [];
        }
    }

    /**
     * Fetch instrument summary
     */
    async fetchInstrumentSummary(instrument) {
        try {
            const response = await fetch(`${this.baseUrl}/api/instruments/${instrument}/summary`);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching instrument summary:', error);
            return null;
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
        console.log('Data cache cleared');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }

    /**
     * Check if currently loading
     */
    getLoadingState() {
        return this.isLoading;
    }

    /**
     * Cancel current request
     */
    cancelRequest() {
        if (this.abortController) {
            this.abortController.abort();
            this.abortController = null;
        }
    }
}

// Export for use in other modules
window.DataFetcher = DataFetcher;
