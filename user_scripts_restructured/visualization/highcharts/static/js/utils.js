/**
 * Utility functions for Highcharts Nautilus Trader Visualization
 */

class ChartUtils {
    /**
     * Format number with appropriate precision
     */
    static formatNumber(value, decimals = 2) {
        if (typeof value !== 'number' || isNaN(value)) {
            return '0.00';
        }
        return value.toFixed(decimals);
    }

    /**
     * Format large numbers with K/M/B suffixes
     */
    static formatLargeNumber(value) {
        if (typeof value !== 'number' || isNaN(value)) {
            return '0';
        }

        const abs = Math.abs(value);
        if (abs >= 1e9) {
            return (value / 1e9).toFixed(1) + 'B';
        } else if (abs >= 1e6) {
            return (value / 1e6).toFixed(1) + 'M';
        } else if (abs >= 1e3) {
            return (value / 1e3).toFixed(1) + 'K';
        }
        return value.toString();
    }

    /**
     * Format timestamp for display
     */
    static formatTimestamp(timestamp, format = 'datetime') {
        const date = new Date(timestamp);
        
        switch (format) {
            case 'time':
                return date.toLocaleTimeString();
            case 'date':
                return date.toLocaleDateString();
            case 'datetime':
                return date.toLocaleString();
            case 'iso':
                return date.toISOString();
            default:
                return date.toString();
        }
    }

    /**
     * Calculate price change and percentage
     */
    static calculatePriceChange(current, previous) {
        if (!current || !previous) {
            return { change: 0, percentage: 0 };
        }

        const change = current - previous;
        const percentage = (change / previous) * 100;

        return {
            change: change,
            percentage: percentage,
            isPositive: change >= 0
        };
    }

    /**
     * Debounce function calls
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle function calls
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Deep merge objects
     */
    static deepMerge(target, source) {
        const output = Object.assign({}, target);
        if (this.isObject(target) && this.isObject(source)) {
            Object.keys(source).forEach(key => {
                if (this.isObject(source[key])) {
                    if (!(key in target)) {
                        Object.assign(output, { [key]: source[key] });
                    } else {
                        output[key] = this.deepMerge(target[key], source[key]);
                    }
                } else {
                    Object.assign(output, { [key]: source[key] });
                }
            });
        }
        return output;
    }

    /**
     * Check if value is object
     */
    static isObject(item) {
        return item && typeof item === 'object' && !Array.isArray(item);
    }

    /**
     * Generate unique ID
     */
    static generateId(prefix = 'id') {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Validate data format
     */
    static validateChartData(data) {
        if (!data || typeof data !== 'object') {
            return { valid: false, error: 'Data is not an object' };
        }

        if (!Array.isArray(data.ohlc)) {
            return { valid: false, error: 'OHLC data is not an array' };
        }

        if (!Array.isArray(data.volume)) {
            return { valid: false, error: 'Volume data is not an array' };
        }

        if (data.ohlc.length !== data.volume.length) {
            return { valid: false, error: 'OHLC and volume data length mismatch' };
        }

        // Validate first few data points
        for (let i = 0; i < Math.min(5, data.ohlc.length); i++) {
            const ohlc = data.ohlc[i];
            if (!Array.isArray(ohlc) || ohlc.length < 5) {
                return { valid: false, error: `Invalid OHLC data at index ${i}` };
            }

            const [timestamp, open, high, low, close] = ohlc;
            if (typeof timestamp !== 'number' || 
                typeof open !== 'number' || 
                typeof high !== 'number' || 
                typeof low !== 'number' || 
                typeof close !== 'number') {
                return { valid: false, error: `Invalid data types at index ${i}` };
            }

            if (high < Math.max(open, close) || low > Math.min(open, close)) {
                return { valid: false, error: `Invalid OHLC values at index ${i}` };
            }
        }

        return { valid: true };
    }

    /**
     * Calculate basic statistics
     */
    static calculateStatistics(data) {
        if (!data || !Array.isArray(data.ohlc) || data.ohlc.length === 0) {
            return null;
        }

        const closes = data.ohlc.map(bar => bar[4]); // Close prices
        const volumes = data.volume.map(vol => vol[1]); // Volume values

        const min = Math.min(...closes);
        const max = Math.max(...closes);
        const first = closes[0];
        const last = closes[closes.length - 1];
        const change = last - first;
        const changePercent = (change / first) * 100;

        const totalVolume = volumes.reduce((sum, vol) => sum + vol, 0);
        const avgVolume = totalVolume / volumes.length;

        return {
            bars: data.ohlc.length,
            priceRange: { min, max },
            priceChange: { absolute: change, percent: changePercent },
            volume: { total: totalVolume, average: avgVolume },
            timeRange: {
                start: data.ohlc[0][0],
                end: data.ohlc[data.ohlc.length - 1][0]
            }
        };
    }

    /**
     * Convert timeframe to milliseconds
     */
    static timeframeToMs(timeframe) {
        const timeframeMap = {
            '1min': 60 * 1000,
            '5min': 5 * 60 * 1000,
            '15min': 15 * 60 * 1000,
            '30min': 30 * 60 * 1000,
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        };
        return timeframeMap[timeframe] || 60 * 1000; // Default to 1 minute
    }

    /**
     * Aggregate data to different timeframe (client-side)
     */
    static aggregateData(data, targetTimeframe) {
        if (!data || !data.ohlc || data.ohlc.length === 0) {
            return data;
        }

        const targetMs = this.timeframeToMs(targetTimeframe);
        const aggregated = { ohlc: [], volume: [] };
        
        let currentBucket = null;
        let bucketStart = null;

        data.ohlc.forEach((bar, index) => {
            const timestamp = bar[0];
            const bucketTime = Math.floor(timestamp / targetMs) * targetMs;

            if (bucketStart !== bucketTime) {
                // Start new bucket
                if (currentBucket) {
                    aggregated.ohlc.push(currentBucket.ohlc);
                    aggregated.volume.push(currentBucket.volume);
                }

                bucketStart = bucketTime;
                currentBucket = {
                    ohlc: [bucketTime, bar[1], bar[2], bar[3], bar[4]], // [time, open, high, low, close]
                    volume: [bucketTime, data.volume[index][1]]
                };
            } else {
                // Update existing bucket
                currentBucket.ohlc[2] = Math.max(currentBucket.ohlc[2], bar[2]); // high
                currentBucket.ohlc[3] = Math.min(currentBucket.ohlc[3], bar[3]); // low
                currentBucket.ohlc[4] = bar[4]; // close (last)
                currentBucket.volume[1] += data.volume[index][1]; // sum volume
            }
        });

        // Add final bucket
        if (currentBucket) {
            aggregated.ohlc.push(currentBucket.ohlc);
            aggregated.volume.push(currentBucket.volume);
        }

        return {
            ...data,
            ohlc: aggregated.ohlc,
            volume: aggregated.volume,
            bars_returned: aggregated.ohlc.length
        };
    }

    /**
     * Log performance metrics
     */
    static logPerformance(operation, startTime, dataSize = null) {
        const duration = performance.now() - startTime;
        const message = `${operation}: ${Math.round(duration)}ms`;
        
        if (dataSize) {
            console.log(`${message} (${dataSize} data points)`);
        } else {
            console.log(message);
        }
        
        return duration;
    }
}

// Export for use in other modules
window.ChartUtils = ChartUtils;
