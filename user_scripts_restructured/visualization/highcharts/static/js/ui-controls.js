/**
 * UI Controls for Highcharts Nautilus Trader Visualization
 * Handles timeframe selection, instrument switching, and chart interactions
 */

class UIControls {
    constructor(chartManager, dataFetcher) {
        this.chartManager = chartManager;
        this.dataFetcher = dataFetcher;
        this.currentInstrument = 'MNQFUT.CME'; // Default as per user preference
        this.currentTimeframe = '1min';
        this.availableTimeframes = ['1min', '5min', '15min', '30min', '1h', '4h', '1d'];
        this.isLoading = false;
        
        this.initializeControls();
        this.bindEvents();
    }

    /**
     * Initialize UI controls
     */
    initializeControls() {
        this.createToolbar();
        this.createLoadingIndicator();
        this.createStatusBar();
    }

    /**
     * Create main toolbar
     */
    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.id = 'chart-toolbar';
        toolbar.className = 'chart-toolbar';
        toolbar.innerHTML = `
            <div class="toolbar-section">
                <label for="instrument-select">Instrument:</label>
                <select id="instrument-select" class="control-select">
                    <option value="MNQFUT.CME">MNQFUT.CME</option>
                </select>
            </div>
            
            <div class="toolbar-section">
                <label for="timeframe-buttons">Timeframe:</label>
                <div id="timeframe-buttons" class="timeframe-buttons">
                    ${this.availableTimeframes.map(tf => 
                        `<button class="timeframe-btn ${tf === this.currentTimeframe ? 'active' : ''}" 
                                data-timeframe="${tf}">${tf}</button>`
                    ).join('')}
                </div>
            </div>
            
            <div class="toolbar-section">
                <button id="refresh-btn" class="control-btn">
                    <span class="btn-icon">🔄</span> Refresh
                </button>
                <button id="fullscreen-btn" class="control-btn">
                    <span class="btn-icon">⛶</span> Fullscreen
                </button>
            </div>
            
            <div class="toolbar-section toolbar-info">
                <span id="data-info" class="data-info"></span>
            </div>
        `;

        // Insert toolbar at the beginning of the container
        const container = document.getElementById('chart-container');
        container.insertBefore(toolbar, container.firstChild);
    }

    /**
     * Create loading indicator
     */
    createLoadingIndicator() {
        const loading = document.createElement('div');
        loading.id = 'loading-indicator';
        loading.className = 'loading-indicator hidden';
        loading.innerHTML = `
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading chart data...</div>
        `;
        
        document.getElementById('chart-container').appendChild(loading);
    }

    /**
     * Create status bar
     */
    createStatusBar() {
        const statusBar = document.createElement('div');
        statusBar.id = 'status-bar';
        statusBar.className = 'status-bar';
        statusBar.innerHTML = `
            <div class="status-section">
                <span id="connection-status" class="status-indicator connected">●</span>
                <span>Connected</span>
            </div>
            <div class="status-section">
                <span id="data-status"></span>
            </div>
            <div class="status-section">
                <span id="performance-status"></span>
            </div>
        `;
        
        document.getElementById('chart-container').appendChild(statusBar);
    }

    /**
     * Bind event handlers
     */
    bindEvents() {
        // Timeframe buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('timeframe-btn')) {
                this.handleTimeframeChange(e.target.dataset.timeframe);
            }
        });

        // Instrument selection
        const instrumentSelect = document.getElementById('instrument-select');
        if (instrumentSelect) {
            instrumentSelect.addEventListener('change', (e) => {
                this.handleInstrumentChange(e.target.value);
            });
        }

        // Refresh button
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.handleRefresh();
            });
        }

        // Fullscreen button
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleFullscreen();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    /**
     * Handle timeframe change
     */
    async handleTimeframeChange(newTimeframe) {
        if (newTimeframe === this.currentTimeframe || this.isLoading) {
            return;
        }

        console.log(`Changing timeframe from ${this.currentTimeframe} to ${newTimeframe}`);

        // Update UI
        this.updateTimeframeButtons(newTimeframe);
        this.currentTimeframe = newTimeframe;

        // Load new data without reloading (client-side aggregation if possible)
        await this.loadChartData();
    }

    /**
     * Handle instrument change
     */
    async handleInstrumentChange(newInstrument) {
        if (newInstrument === this.currentInstrument || this.isLoading) {
            return;
        }

        console.log(`Changing instrument from ${this.currentInstrument} to ${newInstrument}`);
        
        this.currentInstrument = newInstrument;
        await this.loadChartData();
    }

    /**
     * Handle refresh
     */
    async handleRefresh() {
        if (this.isLoading) return;
        
        console.log('Refreshing chart data');
        this.dataFetcher.clearCache();
        await this.loadChartData();
    }

    /**
     * Load chart data
     */
    async loadChartData() {
        if (this.isLoading) return;

        try {
            this.setLoading(true);
            this.updateDataInfo('Loading...');

            const startTime = performance.now();
            const data = await this.dataFetcher.fetchChartData(
                this.currentInstrument, 
                this.currentTimeframe
            );

            if (data) {
                await this.chartManager.updateChart(data);
                
                const loadTime = performance.now() - startTime;
                this.updateDataInfo(`${data.bars_returned || 0} bars loaded`);
                this.updatePerformanceStatus(`${Math.round(loadTime)}ms`);
                
                console.log(`Chart updated successfully in ${Math.round(loadTime)}ms`);
            }

        } catch (error) {
            console.error('Error loading chart data:', error);
            this.updateDataInfo('Error loading data');
            this.showError('Failed to load chart data: ' + error.message);
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Update timeframe buttons
     */
    updateTimeframeButtons(activeTimeframe) {
        const buttons = document.querySelectorAll('.timeframe-btn');
        buttons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.timeframe === activeTimeframe);
        });
    }

    /**
     * Set loading state
     */
    setLoading(loading) {
        this.isLoading = loading;
        const indicator = document.getElementById('loading-indicator');
        const refreshBtn = document.getElementById('refresh-btn');
        
        if (indicator) {
            indicator.classList.toggle('hidden', !loading);
        }
        
        if (refreshBtn) {
            refreshBtn.disabled = loading;
        }
    }

    /**
     * Update data info
     */
    updateDataInfo(text) {
        const dataInfo = document.getElementById('data-info');
        if (dataInfo) {
            dataInfo.textContent = text;
        }
    }

    /**
     * Update performance status
     */
    updatePerformanceStatus(text) {
        const perfStatus = document.getElementById('performance-status');
        if (perfStatus) {
            perfStatus.textContent = text;
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Simple error display - could be enhanced with a proper modal
        alert('Error: ' + message);
    }

    /**
     * Handle keyboard shortcuts
     */
    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'r':
                    e.preventDefault();
                    this.handleRefresh();
                    break;
                case 'f':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        }

        // Timeframe shortcuts (1-7 keys)
        const timeframeIndex = parseInt(e.key) - 1;
        if (timeframeIndex >= 0 && timeframeIndex < this.availableTimeframes.length) {
            this.handleTimeframeChange(this.availableTimeframes[timeframeIndex]);
        }
    }

    /**
     * Toggle fullscreen
     */
    toggleFullscreen() {
        const container = document.getElementById('chart-container');
        
        if (!document.fullscreenElement) {
            container.requestFullscreen().catch(err => {
                console.error('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Debounce resize events
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = setTimeout(() => {
            if (this.chartManager && this.chartManager.chart) {
                this.chartManager.chart.reflow();
            }
        }, 250);
    }

    /**
     * Load available instruments
     */
    async loadInstruments() {
        try {
            const instruments = await this.dataFetcher.fetchInstruments();
            const select = document.getElementById('instrument-select');
            
            if (select && instruments.length > 0) {
                select.innerHTML = instruments.map(instrument =>
                    `<option value="${instrument.id}" ${instrument.id === this.currentInstrument ? 'selected' : ''}>
                        ${instrument.id}
                    </option>`
                ).join('');
            }
        } catch (error) {
            console.error('Error loading instruments:', error);
        }
    }

    /**
     * Get current state
     */
    getCurrentState() {
        return {
            instrument: this.currentInstrument,
            timeframe: this.currentTimeframe,
            isLoading: this.isLoading
        };
    }

    /**
     * Set current state
     */
    setState(state) {
        if (state.instrument) this.currentInstrument = state.instrument;
        if (state.timeframe) this.currentTimeframe = state.timeframe;
        
        this.updateTimeframeButtons(this.currentTimeframe);
        
        const instrumentSelect = document.getElementById('instrument-select');
        if (instrumentSelect) {
            instrumentSelect.value = this.currentInstrument;
        }
    }
}

// Export for use in other modules
window.UIControls = UIControls;
