<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nautilus Trader - Highcharts Visualization</title>
    
    <!-- Highcharts Library -->
    <script src="https://code.highcharts.com/stock/highstock.js"></script>
    <script src="https://code.highcharts.com/stock/modules/exporting.js"></script>
    <script src="https://code.highcharts.com/stock/modules/export-data.js"></script>
    <script src="https://code.highcharts.com/stock/modules/accessibility.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
</head>
<body>
    <div id="app" class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <span class="title-main">Nautilus Trader</span>
                    <span class="title-sub">Highcharts Visualization</span>
                </h1>
                <div class="header-info">
                    <span class="version">v1.0.0</span>
                    <span class="separator">|</span>
                    <span class="engine">Highcharts Engine</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Chart Container -->
            <div id="chart-container" class="chart-container">
                <!-- Toolbar will be inserted here by JavaScript -->
                
                <!-- Chart Area -->
                <div id="chart-area" class="chart-area">
                    <div id="highcharts-chart" class="highcharts-chart"></div>
                </div>
                
                <!-- Loading Indicator will be inserted here by JavaScript -->
                <!-- Status Bar will be inserted here by JavaScript -->
            </div>
        </main>

        <!-- Error Modal -->
        <div id="error-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Error</h3>
                    <button class="modal-close" onclick="closeErrorModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="error-message"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" onclick="closeErrorModal()">OK</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="{{ url_for('static', filename='js/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/chart-config.js') }}"></script>
    <script src="{{ url_for('static', filename='js/data-fetcher.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ui-controls.js') }}"></script>

    <!-- Main Application Script -->
    <script>
        // Application configuration from Flask template
        const APP_CONFIG = {
            defaultInstrument: '{{ default_instrument }}',
            defaultTimeframe: '{{ default_timeframe }}',
            availableTimeframes: {{ available_timeframes | tojson }},
            instruments: {{ instruments | tojson }},
            debug: {{ 'true' if debug else 'false' }}
        };

        // Chart Manager Class
        class ChartManager {
            constructor() {
                this.chart = null;
                this.config = new HighchartsConfig();
                this.currentData = null;
                this.isInitialized = false;
            }

            async initialize() {
                try {
                    console.log('Initializing Highcharts chart manager...');
                    
                    // Create initial empty chart
                    this.chart = Highcharts.stockChart('highcharts-chart', 
                        this.config.getDefaultChartOptions()
                    );
                    
                    this.isInitialized = true;
                    console.log('Chart manager initialized successfully');
                    
                } catch (error) {
                    console.error('Error initializing chart manager:', error);
                    throw error;
                }
            }

            async updateChart(data) {
                if (!this.isInitialized || !this.chart) {
                    throw new Error('Chart manager not initialized');
                }

                try {
                    console.log('Updating chart with new data:', {
                        instrument: data.instrument,
                        timeframe: data.timeframe,
                        bars: data.ohlc?.length || 0
                    });

                    // Validate data
                    const validation = ChartUtils.validateChartData(data);
                    if (!validation.valid) {
                        throw new Error(`Invalid data: ${validation.error}`);
                    }

                    // Store current data
                    this.currentData = data;

                    // Create chart configuration
                    const chartConfig = this.config.createChartConfig(
                        data.instrument,
                        data.timeframe,
                        data
                    );



                    // For stock charts, we need to update series directly
                    // Remove existing series first
                    while(this.chart.series.length > 0) {
                        this.chart.series[0].remove(false);
                    }

                    // Add price series (candlestick)
                    this.chart.addSeries({
                        name: 'Price',
                        type: 'candlestick',
                        data: data.ohlc || [],
                        yAxis: 0,
                        id: 'price-series'
                    }, false);

                    // Add volume series
                    this.chart.addSeries({
                        name: 'Volume',
                        type: 'column',
                        data: data.volume || [],
                        yAxis: 1,
                        color: 'rgba(41, 98, 255, 0.3)',
                        id: 'volume-series'
                    }, false);

                    // Update title
                    this.chart.setTitle({
                        text: `${data.instrument} - ${data.timeframe}`
                    });

                    // Redraw chart
                    this.chart.redraw();

                    console.log('Chart updated successfully');

                } catch (error) {
                    console.error('Error updating chart:', error);
                    throw error;
                }
            }

            destroy() {
                if (this.chart) {
                    this.chart.destroy();
                    this.chart = null;
                }
                this.isInitialized = false;
            }
        }

        // Global Application State
        let chartManager;
        let dataFetcher;
        let uiControls;

        // Error handling
        function showError(message) {
            const modal = document.getElementById('error-modal');
            const messageEl = document.getElementById('error-message');
            messageEl.textContent = message;
            modal.classList.remove('hidden');
        }

        function closeErrorModal() {
            const modal = document.getElementById('error-modal');
            modal.classList.add('hidden');
        }

        // Application initialization
        async function initializeApp() {
            try {
                console.log('Initializing Nautilus Trader Highcharts application...');
                console.log('Configuration:', APP_CONFIG);

                // Initialize components
                chartManager = new ChartManager();
                dataFetcher = new DataFetcher();
                
                // Initialize chart
                await chartManager.initialize();
                
                // Initialize UI controls
                uiControls = new UIControls(chartManager, dataFetcher);
                
                // Set initial state
                uiControls.setState({
                    instrument: APP_CONFIG.defaultInstrument,
                    timeframe: APP_CONFIG.defaultTimeframe
                });

                // Load available instruments
                await uiControls.loadInstruments();

                // Load initial data
                await uiControls.loadChartData();

                console.log('Application initialized successfully');

            } catch (error) {
                console.error('Error initializing application:', error);
                showError('Failed to initialize application: ' + error.message);
            }
        }

        // Handle page unload
        window.addEventListener('beforeunload', () => {
            if (chartManager) {
                chartManager.destroy();
            }
            if (dataFetcher) {
                dataFetcher.cancelRequest();
            }
        });

        // Start application when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeApp);

        // Debug helpers (only in debug mode)
        if (APP_CONFIG.debug) {
            window.debugApp = {
                chartManager,
                dataFetcher,
                uiControls,
                config: APP_CONFIG,
                utils: ChartUtils
            };
            console.log('Debug mode enabled. Access debug tools via window.debugApp');
        }
    </script>
</body>
</html>
