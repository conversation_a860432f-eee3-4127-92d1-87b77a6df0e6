// Chart configuration options for ECharts

// Extended chart options with our custom settings
export interface ChartOptions {
  backgroundColor?: string;
  title?: any;
  legend?: any;
  grid?: any;
  xAxis?: any;
  yAxis?: any;
  dataZoom?: any[];
  series?: any[];
  tooltip?: any;
  animation?: boolean;
  autoSize?: boolean;
  theme?: 'light' | 'dark';
}

export interface VolumeChartOptions extends ChartOptions {
  height?: number; // Volume chart typically shorter
}

export interface CandlestickSeriesOptions {
  type: 'candlestick';
  data: number[][];
  // Standard candlestick options
  itemStyle?: {
    color?: string;        // upColor
    color0?: string;       // downColor
    borderColor?: string;  // borderUpColor
    borderColor0?: string; // borderDownColor
    borderWidth?: number;
  };
}

export interface VolumeSeriesOptions {
  type: 'bar';
  data: number[];
  // Volume-specific options
  itemStyle?: {
    color?: string;
  };
  yAxisIndex?: number;
}

// Use default options directly from chart implementations
// These are defined in the chart managers where they're used