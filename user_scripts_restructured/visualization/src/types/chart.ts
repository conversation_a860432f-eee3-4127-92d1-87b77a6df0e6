

// Chart data types that match ECharts format
export interface OHLCData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
}

export interface VolumeData {
  time: number;
  value: number;
  color?: string;
}

export interface ChartStatistics {
  date_range: {
    start: string;
    end: string;
  };
  bars_count: number;
  data_gaps?: Array<{
    start: string;
    end: string;
    duration: string;
  }>;
}

// Import official data quality schema
import type { BaseDataQuality } from './data-quality';

// Use official data quality interface instead of local definition
export type DataQuality = BaseDataQuality;

export interface ChartResponse {
  ohlc: OHLCData[];
  volume: VolumeData[];
  instrument: string;
  timeframe: string;
  bars_returned: number;
  statistics?: ChartStatistics;
  data_quality?: DataQuality;
  message?: string;
  memory_status?: {
    memory_utilization: number;
    data_points: number;
    is_near_limit?: boolean;
  };
  data_points?: number;
}

// Chart configuration types for ECharts
export interface ChartOptions {
  backgroundColor?: string;
  title?: any;
  legend?: any;
  grid?: any;
  xAxis?: any;
  yAxis?: any;
  dataZoom?: any[];
  series?: any[];
  tooltip?: any;
}

// Series configuration types for ECharts
export interface CandlestickSeriesOptions {
  type: 'candlestick';
  data: number[][];
  itemStyle?: any;
}

export interface VolumeSeriesOptions {
  type: 'bar';
  data: number[];
  itemStyle?: any;
}

// Chart state types
export interface ChartState {
  instrument: string;
  timeframe: Timeframe;
  isLoading: boolean;
  error: string | null;
  data: ChartResponse | null;
  lastUpdate: number;
}

// Timeframe types
export type Timeframe = '1min' | '5min' | '15min' | '30min' | '1h' | '4h' | '1d';

export interface TimeframeButton {
  timeframe: Timeframe;
  label: string;
  active: boolean;
}